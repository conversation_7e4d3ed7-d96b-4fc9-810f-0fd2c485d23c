#!/usr/bin/env python3
"""
GMX Creator - Comprehensive Installation Script
Automatic virtual environment setup and dependency installation for GMX Creator codebase
"""

import subprocess
import sys
import os
import platform
import shutil
from pathlib import Path


class GMXInstaller:
    """Comprehensive installer for GMX Creator with virtual environment management"""

    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.venv_path = self.project_root / "env"
        self.requirements_file = self.project_root / "requirements.txt"
        self.is_windows = platform.system() == "Windows"
        self.python_executable = sys.executable

        # Virtual environment activation paths
        if self.is_windows:
            self.venv_python = self.venv_path / "Scripts" / "python.exe"
            self.venv_pip = self.venv_path / "Scripts" / "pip.exe"
            self.activate_script = self.venv_path / "Scripts" / "activate.bat"
        else:
            self.venv_python = self.venv_path / "bin" / "python"
            self.venv_pip = self.venv_path / "bin" / "pip"
            self.activate_script = self.venv_path / "bin" / "activate"

    def print_header(self):
        """Print installation header"""
        print("🚀 GMX Creator - Comprehensive Installation Script")
        print("=" * 70)
        print("📋 This script will:")
        print("   ✅ Create/verify virtual environment")
        print("   ✅ Install all required dependencies")
        print("   ✅ Verify system compatibility")
        print("   ✅ Set up the complete GMX Creator environment")
        print("=" * 70)
        

    def check_python_version(self):
        """Check if Python version is compatible"""
        print("\n🐍 Checking Python version...")
        python_version = sys.version_info
        print(f"   Current Python: {python_version.major}.{python_version.minor}.{python_version.micro}")

        if python_version < (3, 7):
            print("❌ ERROR: Python 3.7+ is required for GMX Creator")
            print("   Please upgrade Python and try again.")
            return False
        elif python_version < (3, 8):
            print("⚠️  WARNING: Python 3.8+ is recommended for best compatibility")
        else:
            print("✅ Python version is compatible")

        return True

    def check_system_requirements(self):
        """Check system-specific requirements"""
        print("\n💻 Checking system requirements...")

        os_name = platform.system()
        print(f"   Operating System: {os_name}")

        if os_name == "Windows":
            print("✅ Windows detected - all features should work")
            print("📝 Note: Ensure Chrome browser is installed")
        elif os_name == "Linux":
            print("✅ Linux detected")
            print("📝 Note: You may need additional packages:")
            print("   sudo apt-get install python3-tk python3-venv xvfb")
        elif os_name == "Darwin":  # macOS
            print("✅ macOS detected")
            print("📝 Note: Ensure Chrome browser is installed")
        else:
            print(f"⚠️  Unsupported OS: {os_name} - proceed with caution")

        # Check if pip is available
        try:
            subprocess.run([self.python_executable, "-m", "pip", "--version"],
                         check=True, capture_output=True)
            print("✅ pip is available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ ERROR: pip is not available")
            print("   Please install pip and try again.")
            return False

        return True


    def check_chrome_browser(self):
        """Check if Chrome browser is installed"""
        print("\n🌐 Checking for Chrome browser...")

        chrome_paths = [
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ]

        chrome_found = False
        for path in chrome_paths:
            if os.path.exists(path):
                print(f"✅ Chrome found at: {path}")
                chrome_found = True
                break

        if not chrome_found:
            print("⚠️  Chrome browser not found in standard locations")
            print("📝 Please install Google Chrome from: https://www.google.com/chrome/")
            print("   GMX Creator requires Chrome for web automation")

        return chrome_found

    def check_virtual_environment(self):
        """Check if virtual environment exists and is valid"""
        print(f"\n📁 Checking virtual environment at: {self.venv_path}")

        if not self.venv_path.exists():
            print("❌ Virtual environment not found")
            return False

        if not self.venv_python.exists():
            print("❌ Virtual environment Python executable not found")
            return False

        try:
            # Test if the virtual environment is working
            result = subprocess.run([str(self.venv_python), "--version"],
                                  capture_output=True, text=True, check=True)
            print(f"✅ Virtual environment found: {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Virtual environment is corrupted or invalid")
            return False

    def create_virtual_environment(self):
        """Create a new virtual environment"""
        print(f"\n🔧 Creating virtual environment at: {self.venv_path}")

        try:
            # Remove existing broken virtual environment if it exists
            if self.venv_path.exists():
                print("   Removing existing broken virtual environment...")
                shutil.rmtree(self.venv_path)

            # Create new virtual environment
            print("   Creating new virtual environment...")
            subprocess.run([self.python_executable, "-m", "venv", str(self.venv_path)],
                         check=True)

            # Verify creation
            if self.check_virtual_environment():
                print("✅ Virtual environment created successfully")
                return True
            else:
                print("❌ Failed to create valid virtual environment")
                return False

        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            print("   Possible solutions:")
            print("   1. Ensure you have permission to create directories")
            print("   2. Try running as administrator (Windows) or with sudo (Linux/macOS)")
            print("   3. Check if python3-venv is installed (Linux: sudo apt-get install python3-venv)")
            return False
        except Exception as e:
            print(f"❌ Unexpected error creating virtual environment: {e}")
            return False


    def check_requirements_file(self):
        """Check if requirements.txt exists and is readable"""
        print(f"\n📄 Checking requirements file: {self.requirements_file}")

        if not self.requirements_file.exists():
            print("❌ requirements.txt not found")
            print("   Creating a basic requirements.txt file...")
            return self.create_requirements_file()

        try:
            with open(self.requirements_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print("❌ requirements.txt is empty")
                    return self.create_requirements_file()

            print("✅ requirements.txt found and readable")
            return True

        except Exception as e:
            print(f"❌ Error reading requirements.txt: {e}")
            return False

    def create_requirements_file(self):
        """Create a comprehensive requirements.txt file for GMX Creator"""
        print("🔧 Creating requirements.txt for GMX Creator...")

        requirements_content = """# GMX Creator - Comprehensive Dependencies
# Generated by install_requirements.py
# Updated based on actual codebase analysis

# Core web automation packages
selenium>=4.0.0
seleniumbase>=4.0.0
webdriver-manager>=3.8.0
undetected-chromedriver>=3.4.0

# HTTP and networking
requests>=2.25.0

# Image processing and computer vision
pillow>=8.0.0
opencv-python>=4.5.0
numpy>=1.20.0

# GUI automation and system utilities
pyautogui>=0.9.50
psutil>=5.8.0

# Environment and configuration
python-dotenv>=0.19.0

# CAPTCHA solving services
2captcha-python>=1.1.0
anticaptchaofficial>=1.0.0
solvecaptcha-python>=1.0.0

# OCR functionality
rapidocr>=1.3.0
"""

        try:
            with open(self.requirements_file, 'w', encoding='utf-8') as f:
                f.write(requirements_content)
            print("✅ Created comprehensive requirements.txt")
            return True
        except Exception as e:
            print(f"❌ Failed to create requirements.txt: {e}")
            return False

    def install_package(self, package, use_venv=True):
        """Install a package using pip in the virtual environment"""
        try:
            pip_executable = str(self.venv_pip) if use_venv else "pip"
            python_executable = str(self.venv_python) if use_venv else self.python_executable

            # Try using pip directly first
            try:
                subprocess.run([pip_executable, "install", package],
                             check=True, capture_output=True, text=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                # Fallback to python -m pip
                subprocess.run([python_executable, "-m", "pip", "install", package],
                             check=True, capture_output=True, text=True)

            print(f"✅ Successfully installed {package}")
            return True

        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}")
            if e.stderr:
                print(f"   Error: {e.stderr.strip()}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error installing {package}: {e}")
            return False

    def check_package_installed(self, package, use_venv=True):
        """Check if a package is installed in the virtual environment"""
        try:
            python_executable = str(self.venv_python) if use_venv else self.python_executable

            # Handle special cases for package name mapping
            import_name = package
            package_mapping = {
                "pillow": "PIL",
                "opencv-python": "cv2",
                "2captcha-python": "twocaptcha",
                "anticaptchaofficial": "anticaptchaofficial",
                "webdriver-manager": "webdriver_manager",
                "undetected-chromedriver": "undetected_chromedriver",
                "python-dotenv": "dotenv",
                "solvecaptcha-python": "solvecaptcha",
                "rapidocr": "rapidocr"
            }

            if package in package_mapping:
                import_name = package_mapping[package]

            # Test import in the virtual environment
            result = subprocess.run([python_executable, "-c", f"import {import_name}"],
                                  capture_output=True, text=True)
            return result.returncode == 0

        except Exception:
            return False


    def install_from_requirements(self):
        """Install packages from requirements.txt"""
        print(f"\n📦 Installing packages from requirements.txt...")

        try:
            # Read requirements file
            with open(self.requirements_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Parse requirements
            packages = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('-'):
                    # Handle version specifiers
                    package_name = line.split('>=')[0].split('==')[0].split('<')[0].split('>')[0]
                    packages.append(line)  # Keep full requirement specification

            if not packages:
                print("❌ No packages found in requirements.txt")
                return False

            print(f"   Found {len(packages)} packages to install")

            # Install packages
            installed_count = 0
            failed_packages = []

            for package in packages:
                package_name = package.split('>=')[0].split('==')[0].split('<')[0].split('>')[0]

                if self.check_package_installed(package_name):
                    print(f"✅ {package_name} already installed")
                    installed_count += 1
                    continue

                print(f"📥 Installing {package}...")
                if self.install_package(package):
                    installed_count += 1
                else:
                    failed_packages.append(package)

            # Summary
            print(f"\n📊 Installation Summary:")
            print(f"   ✅ Successfully installed: {installed_count}")
            print(f"   ❌ Failed to install: {len(failed_packages)}")

            if failed_packages:
                print(f"\n❌ Failed packages:")
                for pkg in failed_packages:
                    print(f"   - {pkg}")
                print("\n🔧 Try installing failed packages manually:")
                for pkg in failed_packages:
                    print(f"   {self.venv_pip} install {pkg}")
                return False

            print("✅ All packages installed successfully!")
            return True

        except Exception as e:
            print(f"❌ Error installing from requirements.txt: {e}")
            return False

    def upgrade_pip(self):
        """Upgrade pip in the virtual environment"""
        print("\n🔧 Upgrading pip in virtual environment...")
        try:
            subprocess.run([str(self.venv_python), "-m", "pip", "install", "--upgrade", "pip"],
                         check=True, capture_output=True)
            print("✅ pip upgraded successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Failed to upgrade pip: {e}")
            print("   Continuing with current pip version...")
            return False

    def verify_installation(self):
        """Verify that key packages are working correctly"""
        print("\n🔍 Verifying installation...")

        critical_packages = [
            ("selenium", "from selenium import webdriver"),
            ("seleniumbase", "import seleniumbase"),
            ("requests", "import requests"),
            ("PIL", "from PIL import Image"),
            ("pyautogui", "import pyautogui"),
            ("psutil", "import psutil"),
            ("numpy", "import numpy"),
            ("cv2", "import cv2"),
            ("twocaptcha", "from twocaptcha import TwoCaptcha"),
            ("anticaptchaofficial", "from anticaptchaofficial.imagecaptcha import imagecaptcha"),
            ("rapidocr", "from rapidocr import RapidOCR")
        ]

        verification_failed = False

        for package_name, import_test in critical_packages:
            try:
                result = subprocess.run([str(self.venv_python), "-c", import_test],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ {package_name} working correctly")
                else:
                    print(f"❌ {package_name} import failed")
                    verification_failed = True
            except Exception as e:
                print(f"❌ Error testing {package_name}: {e}")
                verification_failed = True

        return not verification_failed


    def print_success_message(self):
        """Print success message with usage instructions"""
        print("\n" + "=" * 70)
        print("🎉 GMX Creator Installation Completed Successfully!")
        print("=" * 70)
        print("📚 Next Steps:")
        print(f"   1. Activate virtual environment:")
        if self.is_windows:
            print(f"      {self.activate_script}")
        else:
            print(f"      source {self.activate_script}")
        print("   2. Run GMX Creator:")
        print("      python gmx.py")
        print("   3. Check logs for any runtime issues")
        print("   4. Ensure Chrome browser is installed and accessible")
        print("\n📁 Project Structure:")
        print(f"   📂 Project Root: {self.project_root}")
        print(f"   📂 Virtual Environment: {self.venv_path}")
        print(f"   📄 Requirements: {self.requirements_file}")
        print("=" * 70)

    def print_failure_message(self, error_details=None):
        """Print failure message with troubleshooting steps"""
        print("\n" + "=" * 70)
        print("❌ GMX Creator Installation Failed!")
        print("=" * 70)
        if error_details:
            print(f"🔍 Error Details: {error_details}")
        print("🔧 Troubleshooting Steps:")
        print("   1. Check your internet connection")
        print("   2. Ensure you have sufficient permissions")
        print("   3. Try running as administrator (Windows) or with sudo (Linux/macOS)")
        print("   4. Manually create virtual environment:")
        print(f"      python -m venv {self.venv_path}")
        print("   5. Activate virtual environment and install manually:")
        if self.is_windows:
            print(f"      {self.activate_script}")
        else:
            print(f"      source {self.activate_script}")
        print(f"      pip install -r {self.requirements_file}")
        print("   6. Check Python and pip versions")
        print("   7. Install system dependencies (Linux: python3-venv, python3-tk)")
        print("=" * 70)

    def run_installation(self):
        """Main installation process"""
        self.print_header()

        # Step 1: Check system requirements
        if not self.check_python_version():
            self.print_failure_message("Python version incompatible")
            return False

        if not self.check_system_requirements():
            self.print_failure_message("System requirements not met")
            return False

        # Step 2: Check Chrome browser (warning only)
        self.check_chrome_browser()

        # Step 3: Handle virtual environment
        if not self.check_virtual_environment():
            if not self.create_virtual_environment():
                self.print_failure_message("Failed to create virtual environment")
                return False

        # Step 4: Check/create requirements file
        if not self.check_requirements_file():
            self.print_failure_message("Failed to create requirements.txt")
            return False

        # Step 5: Upgrade pip
        self.upgrade_pip()

        # Step 6: Install packages
        if not self.install_from_requirements():
            self.print_failure_message("Package installation failed")
            return False

        # Step 7: Verify installation
        if not self.verify_installation():
            print("⚠️  Some packages may not be working correctly")
            print("   GMX Creator might still work, but some features may be limited")

        # Success!
        self.print_success_message()
        return True


def main():
    """Main entry point"""
    installer = GMXInstaller()

    try:
        success = installer.run_installation()
        return success
    except KeyboardInterrupt:
        print("\n\n⚠️  Installation interrupted by user")
        print("   Run the script again to complete installation")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error during installation: {e}")
        installer.print_failure_message(str(e))
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
